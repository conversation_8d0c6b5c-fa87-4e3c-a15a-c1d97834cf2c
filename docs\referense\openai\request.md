The responses_create endpoint allows you to generate a model response based on various inputs and configurations. Below is a detailed breakdown of the parameters you can use:

Request Body Parameters
background

Type: boolean or null

Optional: Yes

Default: false

Description: Whether to run the model response in the background. Learn more: Background processing.

conversation

Type: string or object

Optional: Yes

Default: null

Description: The conversation that this response belongs to. Items from this conversation are prepended to input_items for this response request. Input items and output items from this response are automatically added to this conversation after this response completes.

Conversation ID: string (The unique ID of the conversation.)

Conversation object: object (The conversation that this response belongs to.)

id: string (Required) The unique ID of the conversation.

(Other properties hidden for brevity, but include similar nested structures like those found in input for message content and roles.)

include

Type: array or null

Optional: Yes

Description: Specify additional output data to include in the model response. Currently supported values are:

code_interpreter_call.outputs: Includes the outputs of python code execution in code interpreter tool call items.

computer_call_output.output.image_url: Include image urls from the computer call output.

file_search_call.results: Include the search results of the file search tool call.

message.input_image.image_url: Include image urls from the input message.

message.output_text.logprobs: Include logprobs with assistant messages.

reasoning.encrypted_content: Includes an encrypted version of reasoning tokens in reasoning item outputs. This enables reasoning items to be used in multi-turn conversations when using the Responses API statelessly (like when the store parameter is set to false, or when an organization is enrolled in the zero data retention program).

input

Type: string or array

Optional: Yes

Description: Text, image, or file inputs to the model, used to generate a response. Learn more: Text inputs and outputs, Image inputs, File inputs, Conversation state, Function calling.

Text input: string (A text input to the model, equivalent to a text input with the user role.)

Input item list: array (A list of one or many input items to the model, containing different content types.)

Input message: object (A message input to the model with a role indicating instruction following hierarchy. Instructions given with the developer or system role take precedence over instructions given with the user role. Messages with the assistant role are presumed to have been generated by the model in previous interactions.)

content: string or array (Required) Text, image, or audio input to the model, used to generate a response. Can also contain previous assistant responses.

role: string (Required) The role of the message input. One of user, assistant, system, or developer.

type: string (Optional) The type of the message input. Always message.

Item: object (An item representing part of the context for the response to be generated by the model. Can contain text, images, and audio inputs, as well as previous assistant responses and tool call outputs. This section contains detailed schema for various item types like Input message, Output message, File search tool call, Computer tool call, Computer tool call output, Web search tool call, Function tool call, Function tool call output, Reasoning, Image generation call, Code interpreter tool call, Local shell call, Local shell call output, MCP list tools, MCP approval request, MCP approval response, MCP tool call, Custom tool call output, Custom tool call.)

Item reference: object (An internal identifier for an item to reference.)

id: string (Required) The ID of the item to reference.

type: string (Optional) Defaults to item_reference. The type of item to reference. Always item_reference.

instructions

Type: string or null

Optional: Yes

Description: A system (or developer) message inserted into the model's context. When using along with previous_response_id, the instructions from a previous response will not be carried over to the next response. This makes it simple to swap out system (or developer) messages in new responses.

max_output_tokens

Type: integer or null

Optional: Yes

Description: An upper bound for the number of tokens that can be generated for a response, including visible output tokens and reasoning tokens.

max_tool_calls

Type: integer or null

Optional: Yes

Description: The maximum number of total calls to built-in tools that can be processed in a response. This maximum number applies across all built-in tool calls, not per individual tool. Any further attempts to call a tool by the model will be ignored.

metadata

Type: map

Optional: Yes

Description: Set of 16 key-value pairs that can be attached to an object. This can be useful for storing additional information about the object in a structured format, and querying for objects via API or the dashboard. Keys are strings with a maximum length of 64 characters. Values are strings with a maximum length of 512 characters.

model

Type: string

Optional: Yes

Description: Model ID used to generate the response, like gpt-4o or o3. OpenAI offers a wide range of models with different capabilities, performance characteristics, and price points. Refer to the model guide to browse and compare available models.

parallel_tool_calls

Type: boolean or null

Optional: Yes

Default: true

Description: Whether to allow the model to run tool calls in parallel.

previous_response_id

Type: string or null

Optional: Yes

Description: The unique ID of the previous response to the model. Use this to create multi-turn conversations. Learn more about conversation state. Cannot be used in conjunction with conversation.

prompt

Type: object or null

Optional: Yes

Description: Reference to a prompt template and its variables. Learn more.

id: string (Required) The unique identifier of the prompt template to use.

variables: map (Optional) Optional map of values to substitute in for variables in your prompt. The substitution values can either be strings, or other Response input types like images or files.

version: string or null (Optional) Optional version of the prompt template.

prompt_cache_key

Type: string

Optional: Yes

Description: Used by OpenAI to cache responses for similar requests to optimize your cache hit rates. Replaces the user field. Learn more.

reasoning

Type: object or null

Optional: Yes

Description: gpt-5 and o-series models only. Configuration options for reasoning models.

effort: string or null (Optional) Defaults to medium. Constrains effort on reasoning for reasoning models. Currently supported values are minimal, low, medium, and high. Reducing reasoning effort can result in faster responses and fewer tokens used on reasoning in a response.

generate_summary: string or null (Optional) Deprecated: use summary instead. A summary of the reasoning performed by the model. This can be useful for debugging and understanding the model's reasoning process. One of auto, concise, or detailed.

summary: string or null (Optional) A summary of the reasoning performed by the model. This can be useful for debugging and understanding the model's reasoning process. One of auto, concise, or detailed.

safety_identifier

Type: string

Optional: Yes

Description: A stable identifier used to help detect users of your application that may be violating OpenAI's usage policies. The IDs should be a string that uniquely identifies each user. We recommend hashing their username or email address, in order to avoid sending us any identifying information. Learn more.

service_tier

Type: string or null

Optional: Yes

Default: auto

Description: Specifies the processing type used for serving the request.

If set to 'auto', then the request will be processed with the service tier configured in the Project settings. Unless otherwise configured, the Project will use 'default'.

If set to 'default', then the request will be processed with the standard pricing and performance for the selected model.

If set to 'flex' or 'priority', then the request will be processed with the corresponding service tier.

When not set, the default behavior is 'auto'.
When the service_tier parameter is set, the response body will include the service_tier value based on the processing mode actually used to serve the request. This response value may be different from the value set in the parameter.

store

Type: boolean or null

Optional: Yes

Default: true

Description: Whether to store the generated model response for later retrieval via API.

stream

Type: boolean or null

Optional: Yes

Default: false

Description: If set to true, the model response data will be streamed to the client as it is generated using server-sent events. See the Streaming section below for more information.

stream_options

Type: object or null

Optional: Yes

Default: null

Description: Options for streaming responses. Only set this when you set stream: true.

include_obfuscation: boolean (Optional) When true, stream obfuscation will be enabled. Stream obfuscation adds random characters to an obfuscation field on streaming delta events to normalize payload sizes as a mitigation to certain side-channel attacks. These obfuscation fields are included by default, but add a small amount of overhead to the data stream. You can set include_obfuscation to false to optimize for bandwidth if you trust the network links between your application and the OpenAI API.

temperature

Type: number or null

Optional: Yes

Default: 1

Description: What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. We generally recommend altering this or top_p but not both.

text

Type: object

Optional: Yes

Description: Configuration options for a text response from the model. Can be plain text or structured JSON data. Learn more: Text inputs and outputs, Structured Outputs.

format: object (Optional) An object specifying the format that the model must output.

Configuring { "type": "json_schema" } enables Structured Outputs, which ensures the model will match your supplied JSON schema. Learn more in the Structured Outputs guide.

The default format is { "type": "text" } with no additional options.

Not recommended for gpt-4o and newer models: Setting to { "type": "json_object" } enables the older JSON mode, which ensures the message the model generates is valid JSON. Using json_schema is preferred for models that support it.

verbosity: string or null (Optional) Defaults to medium. Constrains the verbosity of the model's response. Lower values will result in more concise responses, while higher values will result in more verbose responses. Currently supported values are low, medium, and high.

tool_choice

Type: string or object

Optional: Yes

Description: How the model should select which tool (or tools) to use when generating a response. See the tools parameter to see how to specify which tools the model can call.

Tool choice mode: string (Controls which (if any) tool is called by the model.)

none means the model will not call any tool and instead generates a message.

auto means the model can pick between generating a message or calling one or more tools.

required means the model must call one or more tools.

Allowed tools: object (Constrains the tools available to the model to a pre-defined set.)

mode: string (Required) Constrains the tools available to the model to a pre-defined set. auto allows the model to pick from among the allowed tools and generate a message. required requires the model to call one or more of the allowed tools.

tools: array (Required) A list of tool definitions that the model should be allowed to call.

type: string (Required) Allowed tool configuration type. Always allowed_tools.

Hosted tool: object (Indicates that the model should use a built-in tool to generate a response. Learn more about built-in tools.)

type: string (Required) The type of hosted tool the model should to use. Learn more about built-in tools. Allowed values are: file_search, web_search_preview, computer_use_preview, code_interpreter, image_generation.

Function tool: object (Use this option to force the model to call a specific function.)

name: string (Required) The name of the function to call.

type: string (Required) For function calling, the type is always function.

MCP tool: object (Use this option to force the model to call a specific tool on a remote MCP server.)

server_label: string (Required) The label of the MCP server to use.

type: string (Required) For MCP tools, the type is always mcp.

name: string or null (Optional) The name of the tool to call on the server.

Custom tool: object (Use this option to force the model to call a specific custom tool.)

name: string (Required) The name of the custom tool to call.

type: string (Required) For custom tool calling, the type is always custom.

tools

Type: array

Optional: Yes

Description: An array of tools the model may call while generating a response. You can specify which tool to use by setting the tool_choice parameter. The two categories of tools you can provide the model are:

Built-in tools: Tools that are provided by OpenAI that extend the model's capabilities, like web search or file search. Learn more about built-in tools.

Function calls (custom tools): Functions that are defined by you, enabling the model to call your own code with strongly typed arguments and outputs. Learn more about function calling. You can also use custom tools to call your own code.

Function: object (Defines a function in your own code the model can choose to call. Learn more about function calling.)

name: string (Required) The name of the function to call.

parameters: object (Required) A JSON schema object describing the parameters of the function.

strict: boolean (Required) Whether to enforce strict parameter validation. Default true.

type: string (Required) The type of the function tool. Always function.

description: string (Optional) A description of the function. Used by the model to determine whether or not to call the function.

File search: object (A tool that searches for relevant content from uploaded files. Learn more about the file search tool.)

type: string (Required) The type of the file search tool. Always file_search.

vector_store_ids: array (Required) The IDs of the vector stores to search.

filters: object (Optional) A filter to apply.

max_num_results: integer (Optional) The maximum number of results to return. This number should be between 1 and 50 inclusive.

ranking_options: object (Optional) Ranking options for search.

Web search preview: object (This tool searches the web for relevant results to use in a response. Learn more about the web search tool.)

type: string (Required) The type of the web search tool. One of web_search_preview or web_search_preview_2025_03_11.

search_context_size: string (Optional) High level guidance for the amount of context window space to use for the search. One of low, medium, or high. medium is the default.

user_location: object (Optional) The user's location.

type: string (Required) The type of location approximation. Always approximate.

city: string (Optional) Free text input for the city of the user, e.g. San Francisco.

country: string (Optional) The two-letter ISO country code of the user, e.g. US.

region: string (Optional) Free text input for the region of the user, e.g. California.

timezone: string (Optional) The IANA timezone of the user, e.g. America/Los_Angeles.

Computer use preview: object (A tool that controls a virtual computer. Learn more about the computer tool.)

display_height: integer (Required) The height of the computer display.

display_width: integer (Required) The width of the computer display.

environment: string (Required) The type of computer environment to control.

type: string (Required) The type of the computer use tool. Always computer_use_preview.

MCP tool: object (Give the model access to additional tools via remote Model Context Protocol (MCP) servers. Learn more about MCP.)

server_label: string (Required) A label for this MCP server, used to identify it in tool calls.

type: string (Required) The type of the MCP tool. Always mcp.

allowed_tools: array or object (Optional) List of allowed tool names or a filter object.

authorization: string (Optional) An OAuth access token that can be used with a remote MCP server, either with a custom MCP server URL or a service connector. Your application must handle the OAuth authorization flow and provide the token here.

connector_id: string (Optional) Identifier for service connectors, like those available in ChatGPT. One of server_url or connector_id must be provided. Learn more about service connectors here.

headers: object or null (Optional) Optional HTTP headers to send to the MCP server. Use for authentication or other purposes.

require_approval: object or string (Optional) Defaults to always. Specify which of the MCP server's tools require approval.

server_description: string (Optional) Optional description of the MCP server, used to provide more context.

server_url: string (Optional) The URL for the MCP server. One of server_url or connector_id must be provided.

Code interpreter: object (A tool that runs Python code to help generate a response to a prompt.)

container: string or object (Required) The code interpreter container. Can be a container ID or an object that specifies uploaded file IDs to make available to your code.

type: string (Required) The type of the code interpreter tool. Always code_interpreter.

Image generation tool: object (A tool that generates images using a model like gpt-image-1.)

type: string (Required) The type of the image generation tool. Always image_generation.

background: string (Optional) Defaults to auto. Background type for the generated image. One of transparent, opaque, or auto.

input_fidelity: string or null (Optional) Defaults to low. Control how much effort the model will exert to match the style and features, especially facial features, of input images. This parameter is only supported for gpt-image-1. Supports high and low.

input_image_mask: object (Optional) Optional mask for inpainting. Contains image_url (string, optional) and file_id (string, optional).

model: string (Optional) Defaults to gpt-image-1. The image generation model to use.

moderation: string (Optional) Defaults to auto. Moderation level for the generated image.

output_compression: integer (Optional) Defaults to 100. Compression level for the output image.

output_format: string (Optional) Defaults to png. The output format of the generated image. One of png, webp, or jpeg.

partial_images: integer (Optional) Defaults to 0. Number of partial images to generate in streaming mode, from 0 (default value) to 3.

quality: string (Optional) Defaults to auto. The quality of the generated image. One of low, medium, high, or auto.

size: string (Optional) Defaults to auto. The size of the generated image. One of 1024x1024, 1024x1536, 1536x1024, or auto.

Local shell tool: object (A tool that allows the model to execute shell commands in a local environment.)

type: string (Required) The type of the local shell tool. Always local_shell.

Custom tool: object (A custom tool that processes input using a specified format. Learn more about custom tools.)

name: string (Required) The name of the custom tool, used to identify it in tool calls.

type: string (Required) The type of the custom tool. Always custom.

description: string (Optional) Optional description of the custom tool, used to provide more context.

format: object (Optional) The input format for the custom tool. Default is unconstrained text.

top_logprobs

Type: integer or null

Optional: Yes

Description: An integer between 0 and 20 specifying the number of most likely tokens to return at each token position, each with an associated log probability.

top_p

Type: number or null

Optional: Yes

Default: 1

Description: An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered. We generally recommend altering this or temperature but not both.

truncation

Type: string or null

Optional: Yes

Default: disabled

Description: The truncation strategy to use for the model response.

auto: If the context of this response and previous ones exceeds the model's context window size, the model will truncate the response to fit the context window by dropping input items in the middle of the conversation.

disabled (default): If a model response will exceed the context window size for a model, the request will fail with a 400 error.

user

Type: string

Optional: Yes

Deprecated: This field is being replaced by safety_identifier and prompt_cache_key. Use prompt_cache_key instead to maintain caching optimizations.

Description: A stable identifier for your end-users. Used to boost cache hit rates by better bucketing similar requests and to help OpenAI detect and prevent abuse. Learn more.
