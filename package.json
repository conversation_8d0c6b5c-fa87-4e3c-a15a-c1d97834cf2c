{"name": "bug-v3", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@eslint/js": "^9.34.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@google/genai": "^1.15.0", "@tailwindcss/postcss": "^4.1.12", "@typescript-eslint/eslint-plugin": "^8.40.0", "ant-design-vue": "^4.2.6", "autoprefixer": "^10.4.21", "monaco-editor": "^0.52.2", "openai": "^5.15.0", "pinia": "^3.0.3", "tailwindcss": "^4.1.12", "uuid": "^11.1.0", "vue": "^3.5.18"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.16.5", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "sortablejs": "^1.15.6", "typescript": "~5.8.0", "unplugin-vue-components": "^29.0.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}